import { create } from "zustand";
import { REPORT_SECTIONS } from "~/helpers/constants";
import { ReportMetadata, ReportSection } from "~/types/global";
import { Draft, produce } from "immer";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { toast } from "sonner";

// STORE
interface ReportDetailStoreActions {
    setReportSections: (sections: ReportSection[]) => void;
    setDeletedSections: (sections: ReportSection[]) => void;
    setReportType: (type: string) => void;
    setMetadata: (metadata: ReportMetadata) => void;
    setProfileImage: (image: string) => void;
    updateSectionEntries: (
        sectionTitle: string,
        updaterFn: (entry: Draft<ReportSection["data"][0]>, index: number) => void,
        testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
        testSectionDeletedFn: (section: ReportSection) => boolean
    ) => void;
}

interface ReportDetailStoreState {
    sections: ReportSection[];
    deletedSections: ReportSection[];
    metadata: ReportMetadata | null;
    reportType: string;
    profileImage: string | null;
    actions: ReportDetailStoreActions;
}

const useReportDetailStore = create<ReportDetailStoreState>((set) => ({
    sections: [],
    deletedSections: [],
    metadata: null,
    reportType: "",
    profileImage: null,
    actions: {
        setReportSections: (sections) => {
            const imagensSection = sections.find(
                (section) => section.title === REPORT_SECTIONS.imagens
            );
            if (imagensSection) {
                const firstImage = imagensSection.data[0].detalhes.find(
                    (d: any) => d.value.url
                );
                if (firstImage) {
                    set({ profileImage: firstImage.value.url.value });
                }
            } else {
                set({ profileImage: null });
            }
            set({ sections });
        },

        setDeletedSections: (deletedSections) => set({ deletedSections }),

        setMetadata: (metadata) => set({ metadata }),

        setReportType: (reportType) => set({ reportType }),

        setProfileImage: (profileImage) => set({ profileImage }),

        updateSectionEntries: (
            sectionTitle,
            updaterFn,
            testEntryDeletedFn,
            testSectionDeletedFn
        ) =>
            set((state) =>
                produce(state, (draft) => {
                    const section = draft.sections.find(
                        (s) => s.title === sectionTitle
                    );
                    if (!section) return;

                    // 1) apply the mutation you passed, *with* its index
+                   section.data.forEach((entry, i) => updaterFn(entry as any, i))

                    // 2) re-test every entry
                    const allEntriesDeleted = section.data.every((e) =>
                        testEntryDeletedFn(e as any)
                    );

                    // 3) set the section flag however *you* want
                    section.is_deleted = testSectionDeletedFn(section);
                })
            ),
    },
}));

export const useReportSections = () =>
    useReportDetailStore((state) => state.sections);
export const useReportDeletedSections = () =>
    useReportDetailStore((state) => state.deletedSections);
export const useReportMetadata = () =>
    useReportDetailStore((state) => state.metadata);
export const useReportType = () =>
    useReportDetailStore((state) => state.reportType);
export const useReportProfileImage = () =>
    useReportDetailStore((state) => state.profileImage);
export const useReportDetailActions = () =>
    useReportDetailStore((state) => state.actions);

export function useReportActionsWithAutoSave() {
    const actions = useReportDetailStore((state) => state.actions);
    const { addNewReportMutation, invalidateReportDetails } = useReportCRUD();

    // Override only updateSectionEntries
    const updateSectionEntries = (
        sectionTitle: string,
        updaterFn: (entry: any, index: number) => void,
        testEntryDeletedFn: (entry: any) => boolean,
        testSectionDeletedFn: (section: any) => boolean
    ) => {
        // Atualizar in-memory
        actions.updateSectionEntries(
            sectionTitle,
            updaterFn,
            testEntryDeletedFn,
            testSectionDeletedFn
        );

        const { sections, metadata } = useReportDetailStore.getState();
        const reportId = metadata?.user_reports_id as string;

        const payload = {
            ...metadata,
            data: { [metadata?.report_type as string]: sections },
        };

        console.log("[useReportActionsWithAutoSave] payload", payload);

        addNewReportMutation.mutate(payload, {
            onSuccess: () => {
                toast.success("Salvo com sucesso!");
                if (reportId) invalidateReportDetails(reportId);
            },
            onError: (error) => {
                console.error("[AutoSave] Failed to save, invalidating anyway", error);
                toast.error("Falha ao salvar, tente novamente mais tarde.");
                if (reportId) invalidateReportDetails(reportId);
            },
        });
    };

    return {
        ...actions,
        updateSectionEntries,
    };
}