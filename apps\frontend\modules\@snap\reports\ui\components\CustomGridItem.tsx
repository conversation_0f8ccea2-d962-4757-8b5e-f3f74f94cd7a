import { useState } from "react";
import { GridItem } from "@snap/design-system";
import { useReportMode, useIsTrashEnabled } from "../../context/ReportContext";
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";

interface GridItemProps {
  cols?: 1 | 2 | 3;
  fullWidth?: boolean;
  children: React.ReactNode;
  className?: string;
}

interface CustomGridItemProps extends GridItemProps {
  onToggleField?: () => void;
  is_selected?: boolean;
  is_deleted?: boolean;
  source?: string[];
}

export const CustomGridItem: React.FC<CustomGridItemProps> = ({
  className = "",
  onToggleField,
  is_selected = false,
  is_deleted = false,
  source = [],
  ...restProps
}) => {
  const mode = useReportMode();
  const isTrashEnabled = useIsTrashEnabled();
  const isPrintMode = mode === "print-pdf";
  const isTrashMode = mode === "trash";
  const [isSelected, setIsSelected] = useState(is_selected);

  const handleToggle = () => {
    setIsSelected(!isSelected);
    if (onToggleField) {
      onToggleField();
    }
  };

  return (
    <div className="relative group">
      <GridItem {...restProps} className={`${isSelected ? "opacity-50" : ""}` + className} />
      {
        !isPrintMode && isTrashEnabled && (
          <button onClick={handleToggle}
            className={
              `absolute
              right-4
              top-2
              transform -translate-y-2
              w-4 h-4
              opacity-0
              group-hover:opacity-100
              cursor-pointer
              disabled:opacity-50
              transition-opacity duration-200
              `
            }
          >
           {
              isTrashMode ? (
                <LiaTrashRestoreAltSolid size={32} color="var(--foreground)" title="Restaurar"/>
              ) : (
                <LiaTrashAltSolid size={32} color="var(--primary)" title="deletar"/>
              )
           }
          </button>
        )
      }

      {/* TODO - usar para o modo de seleção multipla */}
      {/* <input
        type="checkbox"
        defaultChecked={isSelected}
        disabled={mode === "print-pdf"}
        onChange={handleToggle}
        className="
          absolute
          right-2
          top-2
          transform -translate-y-2

          w-4 h-4
          bg-gray-200
          border border-gray-300
          rounded

          opacity-0
          group-hover:opacity-100
          checked:opacity-100

          cursor-pointer
          disabled:opacity-50

          accent-primary

          transition-opacity duration-200
        "
      /> */}
    </div>
  );
};
