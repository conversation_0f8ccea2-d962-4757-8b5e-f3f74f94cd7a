import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { DiarioOficialCPF } from "../../model/DiariosOficiaisCPF";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderDiariosOficiaisCPFArray(
  sectionTitle: string
): ArrayRenderStrategy<DiarioOficialCPF> {
  const actions = useReportActions()!;
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted)
      : false;

  const testSectionDeleted = (sec: any) =>
    Array.isArray(sec.data) && sec.data.every(testDetalhesDeleted);

  const createFormatByKey = (dataArray: DiarioOficialCPF[]) => {
    const idxMap = new Map<DiarioOficialCPF, number>();
    dataArray.forEach((e, i) => idxMap.set(e, i));

    return {
      local: (entry?: DiarioOficialCPF) => {
        if (!entry?.local || !includeKey(entry.local.is_deleted || false)) return null;
        const idx = idxMap.get(entry)!;
        return (
          <CustomGridContainer cols={3} key={`local-${idx}`}>
            <CustomGridItem
              cols={1}
              className="mb-6"
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e, i) => {
                    if (i === idx && e.local) {
                      e.local.is_deleted = !e.local.is_deleted;
                    }
                  },
                  e => e.local?.is_deleted === true,
                  testSectionDeleted
                )
              }
            >
              <CustomReadOnlyInputField
                label={entry.local.label.toUpperCase()}
                colorClass="bg-primary"
                value={String(entry.local.value)}
                tooltip={renderSourceTooltip(entry.local.source)}
              />
            </CustomGridItem>
          </CustomGridContainer>
        );
      },

      detalhes: (entry?: DiarioOficialCPF) => {
        if (!entry?.detalhes) return null;
        const subs = Object.entries(entry.detalhes).filter(([, v]) =>
          includeKey((v as any).is_deleted)
        );
        if (!subs.length) return null;
        const idx = idxMap.get(entry)!;
        return (
          <CustomGridContainer
            cols={2}
            columnFirst
            key={`detalhes-${idx}`}
            className="mb-6"
          >
            {subs.map(([fieldKey, val]) => (
              <CustomGridItem
                key={fieldKey}
                cols={1}
                onToggleField={() =>
                  actions.updateSectionEntries!(
                    sectionTitle,
                    (e, i) => {
                      if (i === idx) {
                        const d = e.detalhes?.[fieldKey];
                        if (d) d.is_deleted = !d.is_deleted;
                      }
                    },
                    testDetalhesDeleted,
                    testSectionDeleted
                  )
                }
              >
                <CustomReadOnlyInputField
                  label={translatePropToLabel((val as any).label).toUpperCase()}
                  value={parseValue(String((val as any).value))}
                  tooltip={renderSourceTooltip((val as any).source)}
                />
              </CustomGridItem>
            ))}
          </CustomGridContainer>
        );
      },

      ["descrição"]: (entry?: DiarioOficialCPF) => {
        if (!entry?.["descrição"] || !includeKey(entry["descrição"].is_deleted || false)) return null;
        const idx = idxMap.get(entry)!;
        return (
          <CustomGridContainer
            cols={1}
            gap="sm"
            key={`descricao-${idx}`}
            className="mb-6"
          >
            <CustomGridItem
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e, i) => {
                    if (i === idx && e.descricao) {
                      e.descricao.is_deleted = !e.descricao.is_deleted;
                    }
                  },
                  e => e.descricao?.is_deleted === true,
                  testSectionDeleted
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel(entry["descrição"].label).toUpperCase()}
                element="textarea"
                value={String(entry["descrição"].value)}
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                tooltip={renderSourceTooltip(entry["descrição"].source)}
              />
            </CustomGridItem>
          </CustomGridContainer>
        );
      },

      ["texto correspondente"]: (entry?: DiarioOficialCPF) => {
        if (!entry?.["texto correspondente"] || !includeKey(entry["texto correspondente"].is_deleted || false)) return null;
        const idx = idxMap.get(entry)!;
        return (
          <CustomGridContainer
            cols={1}
            gap="sm"
            key={`texto-correspondente-${idx}`}
            className="mb-6"
          >
            <CustomGridItem
              cols={1}
              onToggleField={() =>
                actions.updateSectionEntries!(
                  sectionTitle,
                  (e, i) => {
                    if (i === idx && e["texto correspondente"]) {
                      e["texto correspondente"].is_deleted = !e["texto correspondente"].is_deleted;
                    }
                  },
                  e => e["texto correspondente"]?.is_deleted === true,
                  testSectionDeleted
                )
              }
            >
              <CustomReadOnlyInputField
                label={translatePropToLabel(entry["texto correspondente"].label).toUpperCase()}
                element="textarea"
                value={String(entry["texto correspondente"].value)}
                icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                tooltip={renderSourceTooltip(entry["texto correspondente"].source)}
              />
            </CustomGridItem>
          </CustomGridContainer>
        );
      },
    };
  };

  const validateKeys = (keys: Array<keyof DiarioOficialCPF>): boolean => {
    return keys.some((campo) => campo === "local" || campo === "detalhes" || campo === "descrição" || campo === "texto correspondente");
  };

  const renderSingleItem = (entry: DiarioOficialCPF, formatByKey: any): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof DiarioOficialCPF>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Diários Oficiais CPF] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: DiarioOficialCPF[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Diários Oficiais CPF] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        const hasDeletedLocal = entry.local?.is_deleted === true;
        const hasDeletedDetalhes = entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true);
        const hasDeletedDescricao = entry["descrição"]?.is_deleted === true;
        const hasDeletedOcorrencia = entry["texto correspondente"]?.is_deleted === true;
        return hasDeletedLocal || hasDeletedDetalhes || hasDeletedDescricao || hasDeletedOcorrencia;
      } else {
        const isDeleted = testDetalhesDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const formatByKey = createFormatByKey(filteredData);
    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry, formatByKey);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`diario-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  return {
    validateKeys,
    formatByKey: {},
    render,
    testEntryDeleted: testDetalhesDeleted,
    testSectionDeleted,
  };
}
