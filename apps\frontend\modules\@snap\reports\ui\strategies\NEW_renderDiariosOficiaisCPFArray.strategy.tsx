import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { DiarioOficialCPF } from "../../model/DiariosOficiaisCPF";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderDiariosOficiaisCPFArray(
  sectionTitle: string
): ArrayRenderStrategy<DiarioOficialCPF> {
  const actions = useReportActions()!;
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const includeKey = (isDeleted: boolean) =>
    isTrash ? isDeleted : !isDeleted;

  const testDetalhesDeleted = (e: any) =>
    e.detalhes
      ? Object.values(e.detalhes).every((v: any) => v.is_deleted)
      : false;

  const testSectionDeleted = (sec: any) =>
    Array.isArray(sec.data) && sec.data.every(testDetalhesDeleted);

  const formatByKey: Record<
    string,
    (entry?: DiarioOficialCPF) => React.ReactElement | null
  > = {
    local: (entry) => {
      if (!entry?.local || !includeKey(entry.local.is_deleted || false)) return null;

      const onToggleField = () => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          (e) => {
            if (e === entry && e.local) {
              e.local.is_deleted = !e.local.is_deleted;
            }
          },
          (e) => e.local?.is_deleted === true,
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer cols={3}>
          <CustomGridItem
            cols={1}
            className="mb-6"
            onToggleField={onToggleField}
          >
            <CustomReadOnlyInputField
              label={entry.local.label.toUpperCase()}
              colorClass="bg-primary"
              value={String(entry.local.value)}
              tooltip={renderSourceTooltip(entry.local.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },

    detalhes: (entry) => {
      if (!entry?.detalhes) return null;
      const subs = Object.entries(entry.detalhes).filter(([, v]) =>
        includeKey((v as any).is_deleted)
      );
      if (!subs.length) return null;

      return (
        <CustomGridContainer
          cols={2}
          columnFirst
          className="mb-6"
        >
          {subs.map(([fieldKey, val]) => {
            const onToggleField = () => {
              const updater = actions.updateSectionEntries;
              if (!updater) return;

              updater(
                sectionTitle,
                (e) => {
                  if (e === entry) {
                    const d = e.detalhes?.[fieldKey];
                    if (d) d.is_deleted = !d.is_deleted;
                  }
                },
                testDetalhesDeleted,
                testSectionDeleted
              );
            };

            return (
              <CustomGridItem
                key={fieldKey}
                cols={1}
                onToggleField={onToggleField}
              >
                <CustomReadOnlyInputField
                  label={translatePropToLabel((val as any).label).toUpperCase()}
                  value={parseValue(String((val as any).value))}
                  tooltip={renderSourceTooltip((val as any).source)}
                />
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },

    descricao: (entry) => {
      if (!entry?.descricao || !includeKey(entry.descricao.is_deleted || false)) return null;

      const onToggleField = () => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          (e) => {
            if (e === entry && e.descricao) {
              e.descricao.is_deleted = !e.descricao.is_deleted;
            }
          },
          (e) => e.descricao?.is_deleted === true,
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer
          cols={1}
          gap="sm"
          className="mb-6"
        >
          <CustomGridItem
            cols={1}
            onToggleField={onToggleField}
          >
            <CustomReadOnlyInputField
              label={translatePropToLabel(entry.descricao.label).toUpperCase()}
              element="textarea"
              value={String(entry.descricao.value)}
              icon={<MdOutlineSubdirectoryArrowRight size={16} />}
              tooltip={renderSourceTooltip(entry.descricao.source)}
            />
          </CustomGridItem>
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof DiarioOficialCPF>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (entry: DiarioOficialCPF): React.ReactElement[] => {
    const keys = Object.keys(entry) as Array<keyof DiarioOficialCPF>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Diários Oficiais CPF] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(entry))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: DiarioOficialCPF[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Diários Oficiais CPF] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        const hasDeletedLocal = entry.local?.is_deleted === true;
        const hasDeletedDetalhes = entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true);
        const hasDeletedDescricao = entry.descricao?.is_deleted === true;
        const hasDeletedOcorrencia = entry.ocorrencia?.is_deleted === true;
        return hasDeletedLocal || hasDeletedDetalhes || hasDeletedDescricao || hasDeletedOcorrencia;
      } else {
        const isDeleted = testDetalhesDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((entry, index) => {
      const elements = renderSingleItem(entry);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`diario-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  return {
    validateKeys,
    formatByKey: {},
    render,
    testEntryDeleted: testDetalhesDeleted,
    testSectionDeleted,
  };
}
