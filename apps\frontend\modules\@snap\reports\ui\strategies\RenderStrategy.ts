import { ReactElement } from "react";

export interface RenderStrategy<T = any> {
  validateKeys: (keys: Array<keyof T>) => boolean;
  formatByKey: Record<string, (arg?: T) => ReactElement | null>;
  render: (data: T) => ReactElement[];
  validateData?: (data: T) => boolean;
}

// New interface for array-based rendering strategies
export interface ArrayRenderStrategy<T = any> {
  validateKeys: (keys: Array<keyof T>) => boolean;
  formatByKey: Record<string, (arg?: T) => ReactElement | null>;
  render: (dataArray: T[]) => ReactElement[];
  validateData?: (data: T) => boolean;
  testEntryDeleted?: (entry: T) => boolean;
  testSectionDeleted?: (dataArray: T[]) => boolean;
}