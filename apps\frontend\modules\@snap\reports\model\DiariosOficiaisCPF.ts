import { ValueWithSource } from "./ValueWithSource";

interface _DiarioOficialBase {
    local: string;
    data?: string;
    link?: string;
    "label default key"?: string;
}

interface _DiarioOficialEscavador extends _DiarioOficialBase {
    descricao?: string;
    "texto correspondente"?: string;
    "dados adicionais"?: string;
}

interface _DiarioOficialQueridoDiario extends _DiarioOficialBase {
    uf?: string;
    "edicao extra?"?: string;
    frase?: Array<{ texto: string }>;
}

export type _DiarioOficial = _DiarioOficialBase | _DiarioOficialEscavador | _DiarioOficialQueridoDiario;

export interface Ocorrencia {
    texto: string;
}

export interface DiarioOficialCPF {
    local: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
    descricao?: ValueWithSource<_DiarioOficialEscavador["texto correspondente"] | _DiarioOficialEscavador["descricao"]>;
    ocorrencia?: ValueWithSource<Ocorrencia["texto"]>;
}
