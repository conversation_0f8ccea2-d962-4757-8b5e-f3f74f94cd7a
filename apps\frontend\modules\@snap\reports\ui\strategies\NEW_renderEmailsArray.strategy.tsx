import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { Email } from "../../model/Emails";
import { GridItem } from "@snap/design-system";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderEmailsArray(sectionTitle: string): ArrayRenderStrategy<Email> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrashMode = mode === "trash";

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) => d.is_deleted === true) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const formatByKey: Record<
    string,
    (emails?: Email) => React.ReactElement | null
  > = {
    detalhes: (emails) => {
      if (!emails?.detalhes?.length) return null;
      const indexed = emails.detalhes.map((d, i) => ({ detalhe: d, origIndex: i }));
      const shown = indexed.filter(({ detalhe }) =>
        shouldInclude(detalhe.is_deleted ?? false)
      );

      const onToggleField = (origIndex: number) => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          (entry) => {
            const detalhe = (entry as any).detalhes?.[origIndex];
            if (detalhe) detalhe.is_deleted = !detalhe.is_deleted;
          },
          testEntryDeleted,
          testSectionDeleted
        );
      };

      return (
        <CustomGridContainer cols={1}>
          <GridItem fullWidth>
            <CustomGridContainer cols={2}>
              {shown.map(({ detalhe, origIndex }, renderIdx) => (
                <CustomGridItem
                  key={`email-detalhe-${origIndex}`}
                  cols={1}
                  className="py-2"
                  onToggleField={() => onToggleField(origIndex)}
                >
                  <CustomReadOnlyInputField
                    label={`EMAIL${!isTrashMode ? ` ${renderIdx + 1}` : ""}`}
                    value={String(
                      Object.values(detalhe.value)[0]?.value ?? ""
                    )}
                    tooltip={renderSourceTooltip((Object.values(detalhe.value)[0]?.source))}
                  />
                </CustomGridItem>
              ))}
            </CustomGridContainer>
          </GridItem>
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof Email>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (emails: Email): React.ReactElement[] => {
    const keys = Object.keys(emails) as Array<keyof Email>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Emails] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(emails))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Email[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Emails] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((d: any) => d.is_deleted === true);
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((emails, index) => {
      const elements = renderSingleItem(emails);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`emails-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
  };
}
