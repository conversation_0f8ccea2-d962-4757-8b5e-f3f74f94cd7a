import React from 'react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from './accordion';
import { Badge } from './badge';
import { getStrategyMap } from '../strategies/ReportStrategyFactory';
import { useReportSections } from '../../context/ReportContext';
import ProcessosSection from '../strategies/RenderProcessosStrategy';
import { REPORT_SECTIONS } from '../../config/constants';

const ReportDetailComponent: React.FC = () => {
  const sections = useReportSections() || [];
  const strategyMap = getStrategyMap();

  return (
    <Accordion type="multiple">
      {/* Processos */}
      {sections.map((section, idx) => {
        if (section.title === REPORT_SECTIONS.processos && !section.subsection) {
          const subsections = sections.filter(
            (s) => s.title === REPORT_SECTIONS.processos && !!s.subsection
          );
          return (
            <AccordionItem key={`section-${idx}`} value={`section-${idx}`}>
              <AccordionTrigger className="bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline">
                <div className="flex items-center gap-4">
                  <h3 className="font-mono text-lg uppercase">{section.title}</h3>
                  <Badge variant="secondary" className="rounded-2xl px-4 py-0.5 bg-gray-400">
                    {section.data_count}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-card px-5 pb-0">
                <ProcessosSection rootSection={section} subSections={subsections} />
              </AccordionContent>
            </AccordionItem>
          );
        }

        // pular seções filhas de 'Processos'
        if (section.title === REPORT_SECTIONS.processos && section.subsection) {
          return null;
        }

        // Outras seções
        const printer = strategyMap[section.title];
        if (!printer) {
          console.warn(`No strategy for ${section.title}`);
          return null;
        }
        return (
          <AccordionItem key={`section-${idx}`} value={`section-${idx}`}>
            <AccordionTrigger className="bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline">
              <div className="flex items-center gap-4">
                <h3 className="font-mono text-lg uppercase">{section.title}</h3>
                <Badge variant="secondary" className="rounded-2xl px-4 py-0.5 bg-gray-400">
                  {section.data_count}
                </Badge>
              </div>
            </AccordionTrigger>
            <AccordionContent className="bg-card px-5">
              {section.data.map((item: any, i: number) => (
                <div key={i} className="pt-5">
                  {printer.render(item).map((el: React.ReactNode, j: number) => (
                    <div key={j}>{el}</div>
                  ))}
                </div>
              ))}
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
};

export default ReportDetailComponent;
