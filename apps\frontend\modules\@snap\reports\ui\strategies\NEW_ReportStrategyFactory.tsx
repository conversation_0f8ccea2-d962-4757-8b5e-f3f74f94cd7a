import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
// Hooks para renderizar as seções
import { useRenderDadosPessoas } from "./NEW_renderDadosPessoais.strategy";
import { useRenderEmails } from "./NEW_renderEmails.strategy";
import { useRenderEnderecos } from "./NEW_renderEnderecos.strategy";
import { useRenderDiariosOficiaisCPF } from "./NEW_renderDiariosOficiaisCPF.strategy";

export const useNewStrategyMap = () => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];

  // CPF
  const cpfStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoas(REPORT_SECTIONS.dados_pessoais),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
    [REPORT_SECTIONS.diarios_oficiais_cpf]: useRenderDiariosOficiaisCPF(REPORT_SECTIONS.diarios_oficiais_cpf)
  };

  const cnpjStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.dados_empresa]: useRenderDadosPessoas(REPORT_SECTIONS.dados_empresa),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
    [REPORT_SECTIONS.diarios_oficiais_cnpj]: useRenderDiariosOficiaisCPF(REPORT_SECTIONS.diarios_oficiais_cpf)
  };

  switch (reportType) {
    case "cnpj":
      return cnpjStrategyMap;
    case "telefone":
      return cpfStrategyMap;
    case "cpf":
    default:
      return cpfStrategyMap;
  }
};